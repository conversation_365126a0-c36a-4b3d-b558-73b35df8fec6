_format_version: "3.0"
_transform: true

routes:
- name: solace-producer
  paths:
  - /solace/producer
- name: kafka-producer
  paths:
  - /kafka/producer

plugins:
# - name: solace-upstream
#   route: solace-producer
- name: kafka-upstream
  route: kafka-producer
  config:
    topic: my-topic-incoming
    registry:
      confluent:
        url: http://apicurio:8080/apis/ccompat/v7
    bootstrap_servers:
    - host: kafka1
      port: 9092
    - host: kafka2
      port: 9092
    - host: kafka3
      port: 9092