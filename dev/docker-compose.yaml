name: kong-event-gw-dev
services:
  kafka1:
    image: apache/kafka:3.7.0
    container_name: kafka1
    ports:
      - "9092:9092"
    environment:
      KAFKA_NODE_ID: 1
      KAFKA_PROCESS_ROLES: broker,controller
      <PERSON><PERSON><PERSON>_CONTROLLER_LISTENER_NAMES: CONTROLLER
      KAFKA_LISTENERS: PLAINTEXT://kafka1:9092,CONTROLLER://kafka1:9093
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://localhost:9092
      KAFKA_CONTROLLER_QUORUM_VOTERS: 1@kafka1:9093,2@kafka2:9093,3@kafka3:9093
      KAFKA_CLUSTER_ID: 'abcdefghijklmnopqrstuv'
      KAFKA_LOG_DIRS: /tmp/kraft-combined-logs
    volumes:
      - type: tmpfs
        target: /tmp/kraft-combined-logs

  kafka2:
    image: apache/kafka:3.7.0
    container_name: kafka2
    ports:
      - "9093:9092"
    environment:
      <PERSON>AFKA_NODE_ID: 2
      KAFKA_PROCESS_ROLES: broker,controller
      <PERSON><PERSON><PERSON>_CONTROLLER_LISTENER_NAMES: CONTROLLER
      KAFKA_LISTENERS: PLAINTEXT://kafka2:9092,CONTROLLER://kafka2:9093
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://localhost:9093
      KAFKA_CONTROLLER_QUORUM_VOTERS: 1@kafka1:9093,2@kafka2:9093,3@kafka3:9093
      KAFKA_CLUSTER_ID: 'abcdefghijklmnopqrstuv'
      KAFKA_LOG_DIRS: /tmp/kraft-combined-logs
    volumes:
      - type: tmpfs
        target: /tmp/kraft-combined-logs

  kafka3:
    image: apache/kafka:3.7.0
    container_name: kafka3
    ports:
      - "9094:9092"
    environment:
      KAFKA_NODE_ID: 3
      KAFKA_PROCESS_ROLES: broker,controller
      KAFKA_CONTROLLER_LISTENER_NAMES: CONTROLLER
      KAFKA_LISTENERS: PLAINTEXT://kafka3:9092,CONTROLLER://kafka3:9093
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://localhost:9094
      KAFKA_CONTROLLER_QUORUM_VOTERS: 1@kafka1:9093,2@kafka2:9093,3@kafka3:9093
      KAFKA_CLUSTER_ID: 'abcdefghijklmnopqrstuv'
      KAFKA_LOG_DIRS: /tmp/kraft-combined-logs
    volumes:
      - type: tmpfs
        target: /tmp/kraft-combined-logs

  schema-registry:
    image: apicurio/apicurio-registry:3.0.9
    container_name: schema-registry
    ports:
      - "8080:8080"
  
  schema-registry-ui:
    image: apicurio/apicurio-registry-ui:3.0.9
    container_name: schema-registry-ui
    ports:
      - "8888:8080"

  keycloak:
    image: quay.io/keycloak/keycloak:latest
    container_name: keycloak
    ports:
      - "18080:8080"
    environment:
      - KC_BOOTSTRAP_ADMIN_USERNAME=admin
      - KC_BOOTSTRAP_ADMIN_PASSWORD=admin
    command: start-dev --import-realm
    volumes:
      - ./realm-export.json:/opt/keycloak/data/import/realm-export.json

  kong:
    image: kong/kong-gateway-dev:********-rc.7
    container_name: kong
    ports:
      - "8000:8000"
      - "8443:8443"
    depends_on:
      - kafka1
      - kafka2
      - kafka3
      - keycloak
      - schema-registry
    env_file:
      - konnect.env
    # environment:
        # KONG_LOG_LEVEL: debug

